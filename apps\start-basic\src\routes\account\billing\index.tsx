import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useSession } from '@/lib/auth-client'
import { Link } from '@tanstack/react-router'
import { ArrowLeft } from 'lucide-react'
import { useAutumn, useCustomer } from 'autumn-js/react'

export const Route = createFileRoute('/account/billing/')({
  component: BillingPage,
})

function BillingPage() {
  const { data: session, isPending } = useSession()
  const { attach, check } = useAutumn()
  const { customer, refetch } = useCustomer()
  const [selectedTab, setSelectedTab] = useState<'subscription' | 'billing' | 'usage'>('subscription')

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  // Get subscription data from Autumn
  const currentProduct = customer?.products?.[0] // Get first active product
  const subscription = {
    plan: currentProduct?.name || 'Free Plan',
    status: currentProduct?.status || 'free',
    nextBillingDate: (currentProduct as any)?.next_billing_date ? new Date((currentProduct as any).next_billing_date).toLocaleDateString() : null,
    features: ['50 messages per month', 'Basic support', 'Standard features'] // Default features for now
  }

  // Get usage data from Autumn customer features
  const messagesFeature = customer?.features ? Object.values(customer.features).find((f: any) => f.feature_id === 'messages') : null
  const usage = {
    messagesUsed: messagesFeature ? ((messagesFeature as any).limit - (messagesFeature.balance || 0)) : 0,
    messagesLimit: (messagesFeature as any)?.limit || 50,
    resetDate: (messagesFeature as any)?.reset_date ? new Date((messagesFeature as any).reset_date).toLocaleDateString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
  }

  const handleUpgrade = async (productId: string) => {
    try {
      await attach({ productId })
    } catch (error) {
      console.error('Error upgrading:', error)
      alert('Error starting upgrade process')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-6">
            <Link
              to="/account"
              className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
            >
              <ArrowLeft className="w-5 h-5 mr-1" />
              Back to Account
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">Subscription & Billing</h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">

          {/* Tab Navigation */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              {[
                { key: 'subscription', label: 'Current Plan' },
                { key: 'usage', label: 'Usage' },
                { key: 'billing', label: 'Billing History' },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    selectedTab === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Current Plan Tab */}
          {selectedTab === 'subscription' && (
            <div className="space-y-6">
              {/* Current Plan */}
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{subscription.plan}</h2>
                    <p className="text-sm text-gray-500 mt-1">
                      Status: <span className="text-green-600 font-medium">{subscription.status}</span>
                    </p>
                  </div>
                  <span className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full">
                    Current Plan
                  </span>
                </div>

                <div className="space-y-3 mb-6">
                  {subscription.features.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>

                {subscription.nextBillingDate && (
                  <div className="border-t pt-4">
                    <p className="text-sm text-gray-600">
                      Next billing date: <span className="font-medium">{subscription.nextBillingDate}</span>
                    </p>
                  </div>
                )}
              </div>

              {/* Available Plans */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Available Plans</h2>
                <div className="grid gap-6 md:grid-cols-2">
                  {/* Premium Plan */}
                  <div className="border border-gray-200 rounded-lg p-6">
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">Premium Plan</h3>
                      <p className="text-3xl font-bold text-gray-900 mt-2">
                        $9.99<span className="text-lg font-normal text-gray-600">/month</span>
                      </p>
                    </div>

                    <ul className="space-y-3 mb-6">
                      <li className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">Unlimited messages</span>
                      </li>
                      <li className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">Priority support</span>
                      </li>
                      <li className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">Advanced features</span>
                      </li>
                    </ul>

                    <button
                      onClick={() => handleUpgrade('premium')}
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Upgrade to Premium
                    </button>
                  </div>

                  {/* Pro Plan */}
                  <div className="border border-blue-200 rounded-lg p-6 relative">
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <span className="bg-blue-600 text-white px-3 py-1 text-sm rounded-full">Most Popular</span>
                    </div>

                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">Pro Plan</h3>
                      <p className="text-3xl font-bold text-gray-900 mt-2">
                        $19.99<span className="text-lg font-normal text-gray-600">/month</span>
                      </p>
                    </div>

                    <ul className="space-y-3 mb-6">
                      <li className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">Everything in Premium</span>
                      </li>
                      <li className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">Team collaboration</span>
                      </li>
                      <li className="flex items-center">
                        <svg className="w-4 h-4 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">Analytics dashboard</span>
                      </li>
                    </ul>

                    <button
                      onClick={() => handleUpgrade('pro')}
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Upgrade to Pro
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Usage Tab */}
          {selectedTab === 'usage' && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Usage Statistics</h2>

                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Messages Used</span>
                      <span className="text-sm text-gray-500">{usage.messagesUsed} / {usage.messagesLimit}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${(usage.messagesUsed / usage.messagesLimit) * 100}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Resets on {usage.resetDate}
                    </p>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Need more messages?</h3>
                    <p className="text-gray-600 mb-4">
                      Upgrade to a premium plan for unlimited messages and additional features.
                    </p>
                    <button
                      onClick={() => setSelectedTab('subscription')}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                      View Plans
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Billing History Tab */}
          {selectedTab === 'billing' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Billing History</h2>

              <div className="space-y-4">
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No billing history</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    You haven't made any payments yet. Your billing history will appear here once you subscribe to a plan.
                  </p>
                  <div className="mt-6">
                    <button
                      onClick={() => setSelectedTab('subscription')}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      View Plans
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

        </div>
      </main>
    </div>
  )
}
