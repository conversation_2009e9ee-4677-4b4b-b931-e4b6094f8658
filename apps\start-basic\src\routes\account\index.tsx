import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useSession } from '@/lib/auth-client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Link } from '@tanstack/react-router'
import { CreditCard, User, Settings, Activity, Crown, Zap, Target } from 'lucide-react'
import { LogOutButton } from '@/components/auth/logout-button'
import { useCustomer, useAutumn } from 'autumn-js/react'

export const Route = createFileRoute('/account/')({
  component: AccountPage,
})

function AccountPage() {
  const { data: session, isPending } = useSession()
  const { customer } = useCustomer()
  const { attach } = useAutumn()

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  const user = session.user
  const userInitials = user.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : user.email?.charAt(0).toUpperCase() || 'U'

  // Get subscription data from Autumn
  const currentProduct = customer?.products?.[0] // Get first active product
  const messagesFeature = customer?.features ? Object.values(customer.features).find((f: any) => f.feature_id === 'messages') : null
  const messagesUsed = messagesFeature ? ((messagesFeature as any).limit - (messagesFeature.balance || 0)) : 0
  const messagesLimit = (messagesFeature as any)?.limit || 50

  const handleUpgrade = async (plan: string, billing: string = 'monthly') => {
    try {
      // Navigate to checkout with plan parameters
      window.location.href = `/account/billing/checkout?plan=${plan}&billing=${billing}`
    } catch (error) {
      console.error('Error starting upgrade process:', error)
    }
  }

  return (
    <div className="min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16]">
      {/* Header */}
      <header className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.image || ''} alt={user.name || ''} />
                  <AvatarFallback>{userInitials}</AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{user.name}</span>
              </div>
              <LogOutButton />
            </div>
          </div>
        </div>
      </header>

      {/* Subtitle */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6">
        <p className="text-gray-600 dark:text-gray-400">
          You can manage your account, billing, and team settings here.
        </p>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid gap-6 lg:grid-cols-2">

            {/* Left Column */}
            <div className="space-y-6">

              {/* Basic Information */}
              <Card className="bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <User className="w-5 h-5 mr-2" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4 mb-6">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={user.image || ''} alt={user.name || ''} />
                      <AvatarFallback className="text-lg">{userInitials}</AvatarFallback>
                    </Avatar>
                    <div className="space-y-1">
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                        <p className="text-sm text-gray-900 dark:text-white">{user.firstName} {user.lastName}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                        <p className="text-sm text-gray-900 dark:text-white">{user.email}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Account */}
              <Card className="bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Account</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Current Plan</p>
                      <p className="text-lg font-semibold text-gray-900 dark:text-white">{currentProduct?.name || 'Free Plan'}</p>
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        onClick={() => handleUpgrade('foundation_plan')}
                        className="bg-gray-900 text-white hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100"
                      >
                        Upgrade to Pro
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleUpgrade('performance_plan')}
                        className="border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                      >
                        Upgrade to Business
                      </Button>
                    </div>

                    <div className="pt-2">
                      <p className="text-sm text-gray-600 dark:text-gray-400">Advanced</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

            </div>

            {/* Right Column */}
            <div className="space-y-6">

              {/* Usage */}
              <Card className="bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Usage (Last 30 days)</p>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Premium models</span>
                            <Crown className="w-4 h-4 text-gray-400" />
                          </div>
                          <div className="text-lg font-semibold text-gray-900 dark:text-white">{messagesUsed} / {messagesLimit}</div>
                          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${Math.min((messagesUsed / messagesLimit) * 100, 100)}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            You've used {messagesUsed} requests out of your {messagesLimit} fast requests quota.
                          </p>
                        </div>

                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Free models</span>
                            <Zap className="w-4 h-4 text-gray-400" />
                          </div>
                          <div className="text-lg font-semibold text-gray-900 dark:text-white">0 / 500</div>
                          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            You've used no requests out of your 500 monthly fast requests quota.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

            </div>

          </div>
        </div>
      </main>
    </div>
  )
}
