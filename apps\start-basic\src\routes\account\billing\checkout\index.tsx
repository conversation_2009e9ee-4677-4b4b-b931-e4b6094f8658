import { createFileRoute, useSearch } from '@tanstack/react-router'
import { useState } from 'react'
import { useSession } from '@/lib/auth-client'
import { Link } from '@tanstack/react-router'
import { ArrowLeft, Crown, Zap, Target } from 'lucide-react'
import { useAutumn } from 'autumn-js/react'

export const Route = createFileRoute('/account/billing/checkout/')({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      plan: (search.plan as string) || 'foundation_plan',
      billing: (search.billing as string) || 'monthly',
      from: (search.from as string),
      success_url: (search.success_url as string),
      cancel_url: (search.cancel_url as string),
    }
  },
  component: CheckoutPage,
})

function CheckoutPage() {
  const { plan, billing, from, success_url, cancel_url } = useSearch({ from: '/account/billing/checkout/' })
  const { data: session, isPending } = useSession()
  const { attach } = useAutumn()
  const [isLoading, setIsLoading] = useState(false)

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  // Plan configurations matching database and Autumn setup
  const plans = {
    'foundation_plan': {
      name: 'Foundation Plan',
      icon: Target,
      monthlyPrice: 9.99,
      yearlyPrice: 101.90,
      description: 'Build your fitness foundation with intelligent AI guidance',
      features: [
        'Unlimited workout plans',
        '15 meal scans per month',
        'Form guidance with AI feedback',
        '3 progress photos per month',
        '5 meal planning sessions per month'
      ]
    },
    'performance_plan': {
      name: 'Performance Plan',
      icon: Zap,
      monthlyPrice: 19.99,
      yearlyPrice: 203.90,
      description: 'Unlock advanced AI coaching for serious fitness growth',
      features: [
        'Everything from Foundation Plan',
        'Unlimited meal scans',
        'Advanced form guidance',
        'Unlimited progress photos',
        'Unlimited nutrition coaching',
        'Unlimited meal planning'
      ]
    },
    'champion_plan': {
      name: 'Champion Plan',
      icon: Crown,
      monthlyPrice: 29.99,
      yearlyPrice: 305.90,
      description: 'Elite AI coaching that anticipates your every fitness need',
      features: [
        'Everything from Performance Plan',
        'Unlimited AI coaching sessions',
        'Advanced behavioral learning',
        'Predictive workout adjustments',
        'Priority support'
      ]
    }
  }

  const selectedPlan = plans[plan as keyof typeof plans] || plans['foundation_plan']
  const isYearly = billing === 'yearly'
  const price = isYearly ? selectedPlan.yearlyPrice : selectedPlan.monthlyPrice
  const interval = isYearly ? 'year' : 'month'
  const IconComponent = selectedPlan.icon

  const handleCheckout = async () => {
    setIsLoading(true)
    try {
      console.log('Starting Autumn checkout for plan:', plan)

      // Use Autumn to handle the subscription
      await attach({
        productId: plan,
        // If coming from mobile, handle success/cancel URLs
        ...(from === 'mobile' && success_url && {
          onSuccess: () => {
            if (success_url) {
              window.location.href = decodeURIComponent(success_url)
            }
          }
        }),
        ...(from === 'mobile' && cancel_url && {
          onCancel: () => {
            if (cancel_url) {
              window.location.href = decodeURIComponent(cancel_url)
            }
          }
        })
      })

      // If not from mobile, redirect to account page
      if (from !== 'mobile') {
        window.location.href = '/account?success=true'
      }

    } catch (error) {
      console.error('Checkout error:', error)
      alert('Error starting checkout process')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center py-6">
            <Link
              to="/account"
              className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
            >
              <ArrowLeft className="w-5 h-5 mr-1" />
              Back to Account
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-2xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">

          <div className="mb-8">
            <div className="flex items-center mb-4">
              <IconComponent className="w-8 h-8 mr-3" style={{ color: '#3b82f6' }} />
              <div>
                <h2 className="text-2xl font-bold">{selectedPlan.name}</h2>
                <p className="text-gray-600">{selectedPlan.description}</p>
              </div>
            </div>
            {from === 'mobile' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 text-sm">
                  You'll be redirected back to the mobile app after completing your subscription.
                </p>
              </div>
            )}
          </div>

          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Order Summary</h2>

            <div className="border border-gray-200 rounded-lg p-4 mb-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">{selectedPlan.name}</h3>
                  <p className="text-sm text-gray-500">Billed {interval}ly</p>
                  {isYearly && (
                    <p className="text-sm text-green-600 font-medium">Save 15% with yearly billing</p>
                  )}
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">
                    ${price.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-500">per {interval}</p>
                  {isYearly && (
                    <p className="text-xs text-gray-500">
                      ${(price / 12).toFixed(2)}/month
                    </p>
                  )}
                </div>
              </div>

              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Included features:</h4>
                <ul className="space-y-1">
                  {selectedPlan.features.map((feature, index) => (
                    <li key={index} className="flex items-start text-sm text-gray-600">
                      <svg className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="border-t pt-6">
              <div className="flex justify-between items-center mb-4">
                <span className="text-lg font-medium text-gray-900">Total</span>
                <span className="text-2xl font-bold text-gray-900">
                  ${price.toFixed(2)}/{interval}
                </span>
              </div>

              <button
                onClick={handleCheckout}
                disabled={isLoading}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? 'Processing...' : `Start 3-Day Free Trial`}
              </button>

              <p className="text-xs text-gray-500 mt-4 text-center">
                3-day free trial included. You can cancel your subscription at any time. No long-term commitments.
              </p>
            </div>
          </div>

          <div className="text-center">
            <Link
              to="/account"
              className="text-blue-600 hover:text-blue-700 text-sm"
            >
              ← Back to account settings
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}
