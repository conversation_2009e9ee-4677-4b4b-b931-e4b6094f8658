import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SocialIcon } from "@/components/social-icons";
import LoadingSpinner from "@/components/loading-spinner";
import { signIn, $fetch } from "@/lib/auth-client";
import { useRouter, useSearch } from "@tanstack/react-router";

interface GoogleLoginButtonProps {
  mode?: "login" | "connect";
  onSuccess?: () => void;
  onError?: (error: Error) => void;
  onClick?: () => void;
}

export const GoogleLoginButton = ({
  mode = "login",
  onSuccess,
  onError,
  onClick,
}: GoogleLoginButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearch({ strict: false });

  const handleGoogleAuth = async () => {
    try {
      setIsLoading(true);
      onClick?.();

      // Get the base URL dynamically
      const baseUrl = window.location.origin;

      // Get callback URL from searchParams or use default
      const callbackPath = (searchParams as any)?.callbackUrl || "/account";

      // Construct the redirect URL properly
      const redirectUrl = callbackPath.startsWith("http")
        ? callbackPath
        : `${baseUrl}${callbackPath.startsWith("/") ? callbackPath : `/${callbackPath}`}`;

      console.log("Google Auth Debug:", {
        baseUrl,
        callbackPath,
        redirectUrl,
        userAgent: navigator.userAgent
      });

      // Try the direct fetch approach first (more reliable)
      try {
        console.log("Attempting direct fetch approach...");
        const { data, error } = await $fetch<{ url: string }>("sign-in/social", {
          method: "POST",
          body: {
            provider: "google",
            callbackURL: redirectUrl,
            errorCallbackURL: `${baseUrl}/auth/error`,
          },
        });

        if (data?.url) {
          console.log("Direct fetch successful, redirecting to:", data.url);
          window.location.href = data.url;
          return;
        }

        if (error) {
          console.error("Direct fetch error:", error);
          throw new Error(`Auth request failed: ${error.message || "Unknown error"}`);
        }
      } catch (fetchError) {
        console.warn("Direct fetch method failed, trying library method:", fetchError);

        // Fallback to library method
        try {
          console.log("Attempting library method...");
          await signIn.social({
            provider: "google",
            callbackURL: redirectUrl,
            errorCallbackURL: `${baseUrl}/auth/error`,
            fetchOptions: {
              onSuccess: () => {
                console.log("Library method successful");
                if (mode === "login") {
                  window.location.href = redirectUrl;
                }
                onSuccess?.();
              },
              onError: (ctx) => {
                console.error("Library method error:", ctx.error);
                router.navigate({
                  to: "/auth/error",
                  search: { error: ctx.error.message }
                });
              },
            },
          });
        } catch (libraryError) {
          console.error("Library method also failed:", libraryError);

          // Final fallback to direct URL approach
          console.log("Attempting final fallback - direct URL...");
          const url = new URL("/api/auth/signin/google", baseUrl);
          url.searchParams.set("callbackUrl", redirectUrl);
          window.location.href = url.toString();
          return;
        }
      }
    } catch (error) {
      console.error("Google authentication error:", error);
      onError?.(error instanceof Error ? error : new Error("Authentication failed"));
    } finally {
      setIsLoading(false);
    }
  };

  const buttonText = mode === "login" ? "Sign in with Google" : "Connect Google Calendar";

  return (
    <Button
      variant="outline"
      size="lg"
      className="w-full flex items-center justify-center gap-2 h-12 text-base  dark:bg-gray-800 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600  dark:hover:bg-gray-700 dark:hover:border-gray-500 transition-colors duration-200"
      onClick={handleGoogleAuth}
      disabled={isLoading}
    >
      {isLoading ? <LoadingSpinner /> : <SocialIcon href="#" platform="google" />}
      <span className="ml-2">{buttonText}</span>
    </Button>
  );
};
