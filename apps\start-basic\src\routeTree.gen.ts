/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as IndexImport } from './routes/index'
import { Route as AccountIndexImport } from './routes/account/index'
import { Route as AuthErrorImport } from './routes/auth/error'
import { Route as AdminPromoteImport } from './routes/admin/promote'
import { Route as AdminDashboardImport } from './routes/admin/dashboard'
import { Route as AdminBillingImport } from './routes/admin/billing'
import { Route as AccountBillingCheckoutIndexImport } from './routes/account/billing/checkout/index'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const AccountIndexRoute = AccountIndexImport.update({
  id: '/account/',
  path: '/account/',
  getParentRoute: () => rootRoute,
} as any)

const AuthErrorRoute = AuthErrorImport.update({
  id: '/auth/error',
  path: '/auth/error',
  getParentRoute: () => rootRoute,
} as any)

const AdminPromoteRoute = AdminPromoteImport.update({
  id: '/admin/promote',
  path: '/admin/promote',
  getParentRoute: () => rootRoute,
} as any)

const AdminDashboardRoute = AdminDashboardImport.update({
  id: '/admin/dashboard',
  path: '/admin/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const AdminBillingRoute = AdminBillingImport.update({
  id: '/admin/billing',
  path: '/admin/billing',
  getParentRoute: () => rootRoute,
} as any)

const AccountBillingCheckoutIndexRoute =
  AccountBillingCheckoutIndexImport.update({
    id: '/account/billing/checkout/',
    path: '/account/billing/checkout/',
    getParentRoute: () => rootRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/admin/billing': {
      id: '/admin/billing'
      path: '/admin/billing'
      fullPath: '/admin/billing'
      preLoaderRoute: typeof AdminBillingImport
      parentRoute: typeof rootRoute
    }
    '/admin/dashboard': {
      id: '/admin/dashboard'
      path: '/admin/dashboard'
      fullPath: '/admin/dashboard'
      preLoaderRoute: typeof AdminDashboardImport
      parentRoute: typeof rootRoute
    }
    '/admin/promote': {
      id: '/admin/promote'
      path: '/admin/promote'
      fullPath: '/admin/promote'
      preLoaderRoute: typeof AdminPromoteImport
      parentRoute: typeof rootRoute
    }
    '/auth/error': {
      id: '/auth/error'
      path: '/auth/error'
      fullPath: '/auth/error'
      preLoaderRoute: typeof AuthErrorImport
      parentRoute: typeof rootRoute
    }
    '/account/': {
      id: '/account/'
      path: '/account'
      fullPath: '/account'
      preLoaderRoute: typeof AccountIndexImport
      parentRoute: typeof rootRoute
    }
    '/account/billing/checkout/': {
      id: '/account/billing/checkout/'
      path: '/account/billing/checkout'
      fullPath: '/account/billing/checkout'
      preLoaderRoute: typeof AccountBillingCheckoutIndexImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/admin/promote': typeof AdminPromoteRoute
  '/auth/error': typeof AuthErrorRoute
  '/account': typeof AccountIndexRoute
  '/account/billing/checkout': typeof AccountBillingCheckoutIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/admin/promote': typeof AdminPromoteRoute
  '/auth/error': typeof AuthErrorRoute
  '/account': typeof AccountIndexRoute
  '/account/billing/checkout': typeof AccountBillingCheckoutIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/admin/billing': typeof AdminBillingRoute
  '/admin/dashboard': typeof AdminDashboardRoute
  '/admin/promote': typeof AdminPromoteRoute
  '/auth/error': typeof AuthErrorRoute
  '/account/': typeof AccountIndexRoute
  '/account/billing/checkout/': typeof AccountBillingCheckoutIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/login'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/admin/promote'
    | '/auth/error'
    | '/account'
    | '/account/billing/checkout'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/admin/promote'
    | '/auth/error'
    | '/account'
    | '/account/billing/checkout'
  id:
    | '__root__'
    | '/'
    | '/login'
    | '/admin/billing'
    | '/admin/dashboard'
    | '/admin/promote'
    | '/auth/error'
    | '/account/'
    | '/account/billing/checkout/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  LoginRoute: typeof LoginRoute
  AdminBillingRoute: typeof AdminBillingRoute
  AdminDashboardRoute: typeof AdminDashboardRoute
  AdminPromoteRoute: typeof AdminPromoteRoute
  AuthErrorRoute: typeof AuthErrorRoute
  AccountIndexRoute: typeof AccountIndexRoute
  AccountBillingCheckoutIndexRoute: typeof AccountBillingCheckoutIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  LoginRoute: LoginRoute,
  AdminBillingRoute: AdminBillingRoute,
  AdminDashboardRoute: AdminDashboardRoute,
  AdminPromoteRoute: AdminPromoteRoute,
  AuthErrorRoute: AuthErrorRoute,
  AccountIndexRoute: AccountIndexRoute,
  AccountBillingCheckoutIndexRoute: AccountBillingCheckoutIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/login",
        "/admin/billing",
        "/admin/dashboard",
        "/admin/promote",
        "/auth/error",
        "/account/",
        "/account/billing/checkout/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/admin/billing": {
      "filePath": "admin/billing.tsx"
    },
    "/admin/dashboard": {
      "filePath": "admin/dashboard.tsx"
    },
    "/admin/promote": {
      "filePath": "admin/promote.tsx"
    },
    "/auth/error": {
      "filePath": "auth/error.tsx"
    },
    "/account/": {
      "filePath": "account/index.tsx"
    },
    "/account/billing/checkout/": {
      "filePath": "account/billing/checkout/index.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
