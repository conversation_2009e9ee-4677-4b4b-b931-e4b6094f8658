// adaptive-login-form.tsx
import { useState, useEffect } from "react";
import { useRouter, useSearch } from "@tanstack/react-router";
import { GoogleLoginButton } from "@/components/auth/google-login-button";
import { GithubLoginButton } from "@/components/auth/github-login-button";
import { ArrowLeft, Smartphone, Shield, Users } from "lucide-react";

interface AdaptiveLoginFormProps {
  callbackUrl?: string;
}

export default function AdaptiveLoginForm({ callbackUrl = "/account" }: AdaptiveLoginFormProps) {
  const [errorMessage, setErrorMessage] = useState("");
  const [currentImageLoaded, setCurrentImageLoaded] = useState(false);
  const router = useRouter();
  const searchParams = useSearch({ strict: false });

  // Extract parameters from URL
  const from = (searchParams as any)?.from;
  const authProvider = (searchParams as any)?.auth_provider;
  const isFromMobile = from === 'mobile';

  // Handler for auth success
  const handleAuthSuccess = () => {
    router.navigate({ to: callbackUrl });
  };

  // Handler for auth errors
  const handleAuthError = (error: Error) => {
    setErrorMessage(`Authentication failed: ${error.message || "Unknown error"}`);
  };

  // Handler to go back to mobile app
  const handleBackToMobile = () => {
    // Try to open mobile app, fallback to showing message
    const mobileUrl = "mobile://back";
    window.location.href = mobileUrl;

    // Show fallback message after a delay
    setTimeout(() => {
      setErrorMessage("Please return to the mobile app to continue.");
    }, 1000);
  };

  // Determine which auth options to show
  const shouldShowGoogle = !isFromMobile || authProvider === 'google' || authProvider === 'all';
  const shouldShowApple = !isFromMobile || authProvider === 'apple' || authProvider === 'all';
  const shouldShowGithub = !isFromMobile; // Only show GitHub for direct web access

  // Track when the image is loaded
  useEffect(() => {
    setCurrentImageLoaded(false);
    const img = new Image();
    img.src = "/MiloSignin.png";
    img.onload = () => setCurrentImageLoaded(true);
    img.onerror = () => {
      console.warn("Failed to load MiloSignin.png image");
      setCurrentImageLoaded(true); // Still show the placeholder
    };
  }, []);

  return (
    <div className="w-full overflow-hidden rounded-xl">
      {/* Mock window controls with caption */}
      <div className="flex items-center gap-4 px-6 md:px-8 py-3 bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-t-xl">
        <div className="text-[#7e7b76] text-xs text-left md:text-sm font-manrope_1 italic">
          {isFromMobile
            ? "Continue your journey from the mobile app..."
            : "Secure access for existing members only..."
          }
        </div>
      </div>

      {/* Content area */}
      <div>
        {/* Image section */}
        <div className="bg-[#e9e5dc] dark:bg-[#1e1b16] onboarding-image-container overflow-hidden">
          <div className="w-full flex items-center justify-center relative">
            {/* Placeholder that matches the background */}
            <div
              className={`absolute inset-0 bg-[#1e1b16] z-10 transition-opacity duration-300 ${
                currentImageLoaded ? "opacity-0" : "opacity-100"
              }`}
            />

            {/* Actual image */}
            <img
              src="~//MiloSignin.png"
              alt="Milo Sign In"
              className="w-full h-auto max-h-[300px] sm:max-h-[350px] md:max-h-[400px] lg:max-h-[calc(100vh-356px)] opacity-80 onboarding-image relative z-[2]"
              style={{ opacity: currentImageLoaded ? 0.8 : 0 }}
              onLoad={() => setCurrentImageLoaded(true)}
            />
          </div>
        </div>

        {/* Text and OAuth section */}
        <div className="bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-b-xl">
          {/* Text content */}
          <div className="flex flex-col font-manrope_1 items-start justify-center px-4 pt-8 mx-auto w-full max-w-[calc(100%-1rem)] md:max-w-[calc(100%-2rem)]">
            {/* Error message */}
            {errorMessage && (
              <div className="w-full mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <p className="text-red-700 dark:text-red-300 text-sm font-manrope_1">{errorMessage}</p>
              </div>
            )}

            {/* Context-specific messaging */}
            {isFromMobile ? (
              <>
                <h1 className="text-lg font-semibold text-[#7e7b76] mb-2">
                  Continue from Mobile
                </h1>
                {authProvider === 'apple' && (
                  <p className="text-[#7e7b76] text-left text-sm mb-4">
                    Sign in with the same Apple account you used in the mobile app
                  </p>
                )}
                {authProvider === 'google' && (
                  <p className="text-[#7e7b76] text-left text-sm mb-4">
                    Sign in with the same Google account you used in the mobile app
                  </p>
                )}
                {authProvider === 'all' && (
                  <p className="text-[#7e7b76] text-left text-sm mb-4">
                    Sign in with any method since you have multiple authentication options set up
                  </p>
                )}
              </>
            ) : (
              <>
                <h1 className="text-lg font-semibold text-[#7e7b76] mb-2">
                  Sign In to Continue
                </h1>
                <p className="text-[#7e7b76] text-left text-sm mb-4">
                  Choose your preferred sign-in method to access the admin dashboard
                </p>
              </>
            )}
          </div>

          {/* Footer with OAuth buttons (like navigation) */}
          <div className="border-t border-[#d6d1c4] dark:border-[#29261f] mt-8 flex flex-col relative min-h-[120px] rounded-b-xl overflow-hidden">

            {/* Back to mobile button (if mobile flow) */}
            {isFromMobile && (
              <div className="border-b border-[#d6d1c4] dark:border-[#29261f] flex">
                <button
                  type="button"
                  onClick={handleBackToMobile}
                  className="font-manrope_1 transition-colors py-4 px-6 md:px-8 text-left w-full text-[#7e7b76] hover:text-black dark:hover:text-white flex items-center"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Mobile App
                </button>
              </div>
            )}

            {/* OAuth buttons section */}
            <div className="flex-1 p-4 md:p-6 space-y-3">
              {/* Google Login */}
              {shouldShowGoogle && (
                <div className="w-full">
                  <GoogleLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />
                </div>
              )}

              {/* Divider if multiple options */}
              {shouldShowGoogle && shouldShowGithub && (
                <div className="relative my-4">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-[#d6d1c4] dark:border-[#29261f]"></span>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-[#f5f2ea] dark:bg-[#0f0c05] text-[#7e7b76] font-manrope_1">OR</span>
                  </div>
                </div>
              )}

              {/* GitHub Login (web only) */}
              {shouldShowGithub && (
                <div className="w-full">
                  <GithubLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />
                </div>
              )}

              {/* Apple Sign In message (mobile only) */}
              {shouldShowApple && isFromMobile && !shouldShowGoogle && (
                <div className="text-center py-4">
                  <p className="text-sm text-[#7e7b76] mb-2 font-manrope_1">Apple Sign In</p>
                  <p className="text-xs text-[#7e7b76] opacity-70 font-manrope_1">
                    Apple Sign In is only available on iOS devices. Please use the mobile app or try Google Sign In.
                  </p>
                </div>
              )}
            </div>

            {/* Terms footer */}
            <div className="border-t border-[#d6d1c4] dark:border-[#29261f] p-4 md:p-6">
              <div className="text-center text-xs text-[#7e7b76] font-manrope_1">
                <p>
                  By signing in, you agree to our{" "}
                  <a href="/terms" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                    Terms of Service
                  </a>{" "}
                  and{" "}
                  <a href="/privacy" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                    Privacy Policy
                  </a>
                  .
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}