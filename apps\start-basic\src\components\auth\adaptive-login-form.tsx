import { useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useRouter, useSearch } from "@tanstack/react-router";
import { GoogleLoginButton } from "@/components/auth/google-login-button";
import { GithubLoginButton } from "@/components/auth/github-login-button";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Smartphone } from "lucide-react";

interface AdaptiveLoginFormProps {
  callbackUrl?: string;
}

export default function AdaptiveLoginForm({ callbackUrl = "/account" }: AdaptiveLoginFormProps) {
  const [errorMessage, setErrorMessage] = useState("");
  const [debugMode, setDebugMode] = useState(false);
  const router = useRouter();
  const searchParams = useSearch({ strict: false });

  // Extract parameters from URL
  const from = (searchParams as any)?.from;
  const authProvider = (searchParams as any)?.auth_provider;
  const forceWeb = (searchParams as any)?.force_web === 'true'; // For testing
  const isFromMobile = from === 'mobile' && !forceWeb;

  // Debug information
  const debugInfo = {
    from,
    authProvider,
    forceWeb,
    isFromMobile,
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'SSR',
    searchParams: searchParams
  };

  // Handler for auth success
  const handleAuthSuccess = () => {
    router.navigate({ to: callbackUrl });
  };

  // Handler for auth errors
  const handleAuthError = (error: Error) => {
    setErrorMessage(`Authentication failed: ${error.message || "Unknown error"}`);
  };

  // Handler to go back to mobile app
  const handleBackToMobile = () => {
    // Try to open mobile app, fallback to showing message
    const mobileUrl = "mobile://back";
    window.location.href = mobileUrl;

    // Show fallback message after a delay
    setTimeout(() => {
      setErrorMessage("Please return to the mobile app to continue.");
    }, 1000);
  };

  // Determine which auth options to show
  const shouldShowGoogle = !isFromMobile || authProvider === 'google' || authProvider === 'all';
  const shouldShowApple = !isFromMobile || authProvider === 'apple' || authProvider === 'all';
  const shouldShowGithub = !isFromMobile; // Only show GitHub for direct web access

  // Show signup prevention message for direct web access
  if (!isFromMobile) {
    return (
      <>
        {/* Error message */}
        {errorMessage && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}

        {/* Debug Panel */}
        {debugMode && (
          <div className="mb-4 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg text-xs">
            <h4 className="font-semibold mb-2">Debug Info:</h4>
            <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
          </div>
        )}

        {/* Web-only login message */}
        <div className="text-center mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <Smartphone className="w-8 h-8 mx-auto mb-2 text-blue-600" />
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Admin Dashboard Access
          </h3>
          <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
            This is the admin dashboard for existing users only. New users must sign up through our mobile app.
          </p>
          <p className="text-xs text-blue-600 dark:text-blue-400">
            Download our mobile app to create a new account and access all features.
          </p>

          {/* Debug toggle */}
          <button
            onClick={() => setDebugMode(!debugMode)}
            className="mt-2 text-xs text-blue-500 hover:text-blue-700 underline"
          >
            {debugMode ? 'Hide' : 'Show'} Debug Info
          </button>

          {debugMode && (
            <div className="mt-2 text-xs text-blue-600">
              <p>💡 Tip: Add <code>?force_web=true</code> to URL to test web flow even from mobile</p>
            </div>
          )}
        </div>

        {/* Social Login Buttons - All options for web */}
        <div className="space-y-3">
          {/* Google Login Button */}
          <GoogleLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />

          {/* Divider between buttons */}
          <div className="relative my-2">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-300 dark:border-gray-600"></span>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white dark:bg-slate-900 text-gray-500">OR</span>
            </div>
          </div>

          {/* GitHub Login Button */}
          {shouldShowGithub && (
            <GithubLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />
          )}
        </div>

        <div className="text-center text-sm text-muted-foreground mt-4">
          <p>
            By signing in, you agree to our{" "}
            <a href="/terms" className="underline underline-offset-4 hover:text-primary">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="/privacy" className="underline underline-offset-4 hover:text-primary">
              Privacy Policy
            </a>
            .
          </p>
        </div>
      </>
    );
  }

  // Mobile-specific login flow
  return (
    <>
      {/* Error message */}
      {errorMessage && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      )}

      {/* Mobile flow header */}
      <div className="text-center mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
        <Smartphone className="w-8 h-8 mx-auto mb-2 text-green-600" />
        <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
          Continue from Mobile App
        </h3>
        <p className="text-sm text-green-700 dark:text-green-300 mb-3">
          Complete your subscription by signing in with the same account you used in the mobile app.
        </p>

        {/* Back to mobile button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleBackToMobile}
          className="border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-300 dark:hover:bg-green-900/30"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Mobile App
        </Button>
      </div>

      {/* Conditional auth options based on mobile provider */}
      <div className="space-y-3">
        {authProvider === 'apple' && (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Sign in with the same Apple account you used in the mobile app
            </p>
          </div>
        )}

        {authProvider === 'google' && (
          <div className="text-center mb-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Sign in with the same Google account you used in the mobile app
            </p>
          </div>
        )}

        {/* Show Google if it's the provider or if showing all */}
        {shouldShowGoogle && (
          <GoogleLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />
        )}

        {/* Show Apple if it's the provider or if showing all */}
        {shouldShowApple && (
          <div className="text-center py-4">
            <p className="text-sm text-gray-500 mb-2">Apple Sign In</p>
            <p className="text-xs text-gray-400">
              Apple Sign In is only available on iOS devices. Please use the mobile app or try Google Sign In.
            </p>
          </div>
        )}

        {/* Show divider and additional options if showing all */}
        {authProvider === 'all' && shouldShowGoogle && (
          <>
            <div className="relative my-2">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300 dark:border-gray-600"></span>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-slate-900 text-gray-500">OR</span>
              </div>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                You can also sign in with any method since you have multiple authentication options set up.
              </p>
            </div>
          </>
        )}
      </div>

      <div className="text-center text-sm text-muted-foreground mt-4">
        <p>
          By signing in, you agree to our{" "}
          <a href="/terms" className="underline underline-offset-4 hover:text-primary">
            Terms of Service
          </a>{" "}
          and{" "}
          <a href="/privacy" className="underline underline-offset-4 hover:text-primary">
            Privacy Policy
          </a>
          .
        </p>
      </div>
    </>
  );
}
