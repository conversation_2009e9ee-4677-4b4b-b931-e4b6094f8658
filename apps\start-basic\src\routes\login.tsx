import { createFileRoute, redirect } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { auth } from '@/lib/auth'
import LoginPage from '@/components/auth/login-page'

const getSession = createServerFn({
  method: 'GET',
}).handler(async ({ request }) => {
  const session = await auth.api.getSession({
    headers: request.headers,
  })
  return session
})

export const Route = createFileRoute('/login')({
  beforeLoad: async () => {
    const session = await getSession()

    // Redirect authenticated users to account page
    if (session?.user) {
      throw redirect({ to: '/account' })
    }
  },
  component: LoginPage,
})
